@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode */
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 222 47% 11%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Custom colors */
    --brand: 215 100% 50%;
    --brand-foreground: 0 0% 100%;
    --brand-muted: 213 100% 95%;
    --glass-background: 0 0% 100% 0.7;
    --glass-border: 0 0% 100% 0.1;
    --glass-highlight: 0 0% 100% 0.2;
  }

  .dark {
    /* Dark mode */
    --background: 222 47% 5%;
    --foreground: 0 0% 95%;

    --card: 222 47% 8%;
    --card-foreground: 0 0% 95%;

    --popover: 222 47% 8%;
    --popover-foreground: 0 0% 95%;

    --primary: 0 0% 95%;
    --primary-foreground: 222 47% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 0 0% 95%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 95%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Custom colors */
    --brand: 215 100% 50%;
    --brand-foreground: 0 0% 100%;
    --brand-muted: 213 30% 20%;
    --glass-background: 222 47% 10% 0.7;
    --glass-border: 0 0% 30% 0.2;
    --glass-highlight: 0 0% 100% 0.05;
  }
}

@layer base {
  * {
    @apply border-border selection:bg-brand selection:bg-opacity-20 selection:text-foreground;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased overflow-x-hidden;
  }

  html {
    @apply scroll-smooth;
    font-family: "Inter", sans-serif;
  }
}

@layer utilities {
  .glass {
    @apply backdrop-blur-lg bg-background/80 border border-border/50 shadow-sm;
  }

  .glass-card {
    @apply backdrop-blur-xl bg-background/90 border border-border/50 shadow-md;
  }

  .glass-button {
    @apply backdrop-blur-md bg-background/50 border border-border/50 shadow-sm hover:bg-background/70 transition-all;
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-brand to-blue-600 bg-clip-text text-transparent;
  }

  .h1 {
    @apply text-4xl/tight font-bold tracking-tight text-balance md:text-5xl/tight lg:text-6xl/tight;
  }

  .h2 {
    @apply text-3xl font-bold tracking-tight text-balance md:text-4xl;
  }

  .h3 {
    @apply text-2xl font-bold tracking-tight text-balance md:text-3xl;
  }

  .h4 {
    @apply text-xl font-bold tracking-tight text-balance md:text-2xl;
  }

  .p {
    @apply text-base text-muted-foreground md:text-lg;
  }

  /* Animations */
  .animate-fade-up {
    animation: fadeUp 0.5s ease-out forwards;
  }

  .animate-fade-down {
    animation: fadeDown 0.5s ease-out forwards;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
  }

  .animate-voice {
    animation: voice-wave 1s ease-in-out infinite;
  }
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes voice-wave {
  0%, 100% {
    transform: scaleY(0.3);
  }
  50% {
    transform: scaleY(1);
  }
}

/* Button hover effects */
.button-hover-effect {
  position: relative;
  transition: all 0.3s ease;
  z-index: 1;
  overflow: hidden;
}

.button-hover-effect:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.button-hover-effect:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s;
  z-index: -1;
}

.button-hover-effect:hover:before {
  width: 100%;
}

/* Floating button animation */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Add card hover effect */
.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
