"""minor change

Revision ID: 4ee958ccbcb9
Revises: 46e94b456cc0
Create Date: 2025-06-21 18:07:19.573367

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4ee958ccbcb9'
down_revision: Union[str, None] = '46e94b456cc0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('competitive_exams', sa.Column('job_seeker_id', sa.Integer(), nullable=False))
    op.drop_constraint('competitive_exams_user_id_fkey', 'competitive_exams', type_='foreignkey')
    op.create_foreign_key(None, 'competitive_exams', 'job_seekers', ['job_seeker_id'], ['id'], ondelete='CASCADE')
    op.drop_column('competitive_exams', 'user_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('competitive_exams', sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'competitive_exams', type_='foreignkey')
    op.create_foreign_key('competitive_exams_user_id_fkey', 'competitive_exams', 'job_seekers', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_column('competitive_exams', 'job_seeker_id')
    # ### end Alembic commands ###
