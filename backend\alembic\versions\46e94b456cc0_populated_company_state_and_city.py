"""populated company state and city

Revision ID: 46e94b456cc0
Revises: 1ca59a468554
Create Date: 2025-06-16 17:01:06.724261

"""

import json
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.orm import Session

from app.models import City, Country, State


# revision identifiers, used by Alembic.
revision: str = "46e94b456cc0"
down_revision: Union[str, None] = "1ca59a468554"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = Session(bind=bind)

    with open("alembic/countries.json", encoding="utf-8") as f:
        data = json.load(f)

    # countries = [Country(**item) for item in data]
    # session.add_all(countries)
    stmt = sa.insert(Country)
    session.execute(stmt, data)

    with open("alembic/states.json", encoding="utf-8") as f:
        data = json.load(f)

    states = [State(**item) for item in data]
    session.add_all(states)

    with open("alembic/cities.json", encoding="utf-8") as f:
        data = json.load(f)

    cities = [City(**item) for item in data]
    session.add_all(cities)

    with open("alembic/currency.json", encoding="utf-8") as f:
        data = json.load(f)

    for d in data:
        stmt = (
            sa.update(Country)
            .where(Country.id == d["id"])
            .values(currency=d["currency"])
        )

        session.execute(stmt)

    session.commit()
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = Session(bind=bind)

    session.query(Country).delete(synchronize_session=False)
    session.query(State).delete(synchronize_session=False)
    session.query(City).delete(synchronize_session=False)

    session.commit()
    # ### end Alembic commands ###
