"""init

Revision ID: 1ca59a468554
Revises: 
Create Date: 2025-06-16 17:00:31.409786

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ca59a468554'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('companies',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('password_hash', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('email_otp', sa.String(), nullable=True),
    sa.Column('email_otp_expiry', sa.DateTime(), nullable=True),
    sa.Column('email_verified', sa.Boolean(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('phone_otp', sa.String(), nullable=True),
    sa.Column('phone_otp_expiry', sa.DateTime(), nullable=True),
    sa.Column('phone_verified', sa.Boolean(), nullable=True),
    sa.Column('website', sa.String(), nullable=True),
    sa.Column('industry', sa.String(), nullable=True),
    sa.Column('min_company_size', sa.Integer(), nullable=True),
    sa.Column('max_company_size', sa.Integer(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('state', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('zip', sa.String(), nullable=True),
    sa.Column('address', sa.String(), nullable=True),
    sa.Column('banner_url', sa.String(), nullable=True),
    sa.Column('logo_url', sa.String(), nullable=True),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('ratings_count', sa.Integer(), nullable=True),
    sa.Column('tagline', sa.String(), nullable=True),
    sa.Column('tags', sa.String(), nullable=True),
    sa.Column('about_us', sa.String(), nullable=True),
    sa.Column('about_us_poster_url', sa.String(), nullable=True),
    sa.Column('foundation_year', sa.Integer(), nullable=True),
    sa.Column('website_url', sa.String(), nullable=True),
    sa.Column('verified', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_companies_id'), 'companies', ['id'], unique=False)
    op.create_table('countries',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('iso3', sa.String(), nullable=True),
    sa.Column('phonecode', sa.String(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('job_seekers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('firstname', sa.String(), nullable=False),
    sa.Column('lastname', sa.String(), nullable=False),
    sa.Column('gender', sa.String(), nullable=True),
    sa.Column('date_of_birth', sa.DateTime(), nullable=True),
    sa.Column('password_hash', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('email_otp', sa.String(), nullable=True),
    sa.Column('email_otp_expiry', sa.DateTime(), nullable=True),
    sa.Column('email_verified', sa.Boolean(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('phone_otp', sa.String(), nullable=True),
    sa.Column('phone_otp_expiry', sa.DateTime(), nullable=True),
    sa.Column('phone_verified', sa.Boolean(), nullable=True),
    sa.Column('dob', sa.DateTime(), nullable=True),
    sa.Column('current_location', sa.String(), nullable=True),
    sa.Column('home_town', sa.String(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('career_preference_internships', sa.Boolean(), nullable=True),
    sa.Column('career_preference_jobs', sa.Boolean(), nullable=True),
    sa.Column('min_duration_months', sa.Integer(), nullable=True),
    sa.Column('preferred_work_location', sa.String(), nullable=True),
    sa.Column('work_experience_yrs', sa.Integer(), nullable=True),
    sa.Column('updates_subscription', sa.Boolean(), nullable=True),
    sa.Column('key_skills', sa.String(), nullable=True),
    sa.Column('languages', sa.String(), nullable=True),
    sa.Column('profile_summary', sa.String(), nullable=True),
    sa.Column('awards_and_accomplishments', sa.String(), nullable=True),
    sa.Column('resume_url', sa.String(), nullable=True),
    sa.Column('profile_picture_url', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('phone')
    )
    op.create_index(op.f('ix_job_seekers_id'), 'job_seekers', ['id'], unique=False)
    op.create_table('academic_achievements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=False),
    sa.Column('qualification', sa.String(), nullable=True),
    sa.Column('achievements', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_academic_achievements_id'), 'academic_achievements', ['id'], unique=False)
    op.create_table('ai_interviewed_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('department', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('location', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('duration_months', sa.Integer(), nullable=True),
    sa.Column('min_experience', sa.Integer(), nullable=True),
    sa.Column('max_experience', sa.Integer(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('salary_min', sa.Integer(), nullable=True),
    sa.Column('salary_max', sa.Integer(), nullable=True),
    sa.Column('show_salary', sa.Boolean(), nullable=True),
    sa.Column('key_qualification', sa.String(), nullable=True),
    sa.Column('requirements', sa.String(), nullable=True),
    sa.Column('benefits', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('quiz_time_minutes', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_interviewed_jobs_id'), 'ai_interviewed_jobs', ['id'], unique=False)
    op.create_table('certifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=False),
    sa.Column('certification_name', sa.String(), nullable=True),
    sa.Column('certification_provider', sa.String(), nullable=True),
    sa.Column('completion_id', sa.String(), nullable=True),
    sa.Column('certification_url', sa.String(), nullable=True),
    sa.Column('starting_month', sa.Integer(), nullable=True),
    sa.Column('starting_year', sa.Integer(), nullable=True),
    sa.Column('ending_month', sa.Integer(), nullable=True),
    sa.Column('ending_year', sa.Integer(), nullable=True),
    sa.Column('certificate_expires', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_certifications_id'), 'certifications', ['id'], unique=False)
    op.create_table('competitive_exams',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('exam_label', sa.String(), nullable=True),
    sa.Column('score', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_competitive_exams_id'), 'competitive_exams', ['id'], unique=False)
    op.create_table('employment_details',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=False),
    sa.Column('experience_years', sa.Integer(), nullable=True),
    sa.Column('experience_months', sa.Integer(), nullable=True),
    sa.Column('company_name', sa.String(), nullable=True),
    sa.Column('designation', sa.String(), nullable=True),
    sa.Column('starting_month', sa.Integer(), nullable=True),
    sa.Column('starting_year', sa.Integer(), nullable=True),
    sa.Column('ending_month', sa.Integer(), nullable=True),
    sa.Column('ending_year', sa.Integer(), nullable=True),
    sa.Column('is_currently_working', sa.Boolean(), nullable=True),
    sa.Column('work_description', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_employment_details_id'), 'employment_details', ['id'], unique=False)
    op.create_table('higher_educations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=True),
    sa.Column('qualification', sa.String(), nullable=True),
    sa.Column('course_name', sa.String(), nullable=True),
    sa.Column('specialization', sa.String(), nullable=True),
    sa.Column('college_name', sa.String(), nullable=True),
    sa.Column('grading_system', sa.String(), nullable=True),
    sa.Column('grading_system_value', sa.String(), nullable=True),
    sa.Column('starting_year', sa.Integer(), nullable=True),
    sa.Column('passing_year', sa.Integer(), nullable=True),
    sa.Column('course_type', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_higher_educations_id'), 'higher_educations', ['id'], unique=False)
    op.create_table('hsc_educations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=True),
    sa.Column('examination_board', sa.String(), nullable=True),
    sa.Column('medium_of_study', sa.String(), nullable=True),
    sa.Column('actual_percentage', sa.String(), nullable=True),
    sa.Column('passing_year', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('job_seeker_id')
    )
    op.create_index(op.f('ix_hsc_educations_id'), 'hsc_educations', ['id'], unique=False)
    op.create_table('internship_experiences',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=False),
    sa.Column('company_name', sa.String(), nullable=True),
    sa.Column('starting_month', sa.Integer(), nullable=True),
    sa.Column('starting_year', sa.Integer(), nullable=True),
    sa.Column('ending_month', sa.Integer(), nullable=True),
    sa.Column('ending_year', sa.Integer(), nullable=True),
    sa.Column('project_name', sa.String(), nullable=True),
    sa.Column('work_description', sa.String(), nullable=True),
    sa.Column('key_skills', sa.String(), nullable=True),
    sa.Column('project_url', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_internship_experiences_id'), 'internship_experiences', ['id'], unique=False)
    op.create_table('jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('job_title', sa.String(), nullable=True),
    sa.Column('job_role', sa.String(), nullable=True),
    sa.Column('job_location', sa.String(), nullable=True),
    sa.Column('job_locality', sa.String(), nullable=True),
    sa.Column('work_mode', sa.String(), nullable=True),
    sa.Column('min_work_experience', sa.Integer(), nullable=True),
    sa.Column('max_work_experience', sa.Integer(), nullable=True),
    sa.Column('min_salary_per_month', sa.Integer(), nullable=True),
    sa.Column('max_salary_per_month', sa.Integer(), nullable=True),
    sa.Column('additional_benefits', sa.String(), nullable=True),
    sa.Column('skills', sa.String(), nullable=True),
    sa.Column('qualification', sa.String(), nullable=True),
    sa.Column('gender_preference', sa.String(), nullable=True),
    sa.Column('candidate_prev_industry', sa.String(), nullable=True),
    sa.Column('languages', sa.String(), nullable=True),
    sa.Column('education_degree', sa.String(), nullable=True),
    sa.Column('job_description', sa.String(), nullable=True),
    sa.Column('about_company', sa.String(), nullable=True),
    sa.Column('posted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_jobs_id'), 'jobs', ['id'], unique=False)
    op.create_table('projects',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=False),
    sa.Column('project_name', sa.String(), nullable=True),
    sa.Column('starting_month', sa.Integer(), nullable=True),
    sa.Column('starting_year', sa.Integer(), nullable=True),
    sa.Column('ending_month', sa.Integer(), nullable=True),
    sa.Column('ending_year', sa.Integer(), nullable=True),
    sa.Column('project_description', sa.String(), nullable=True),
    sa.Column('key_skills', sa.String(), nullable=True),
    sa.Column('project_url', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_projects_id'), 'projects', ['id'], unique=False)
    op.create_table('ssc_educations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=True),
    sa.Column('examination_board', sa.String(), nullable=True),
    sa.Column('medium_of_study', sa.String(), nullable=True),
    sa.Column('actual_percentage', sa.String(), nullable=True),
    sa.Column('passing_year', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('job_seeker_id')
    )
    op.create_index(op.f('ix_ssc_educations_id'), 'ssc_educations', ['id'], unique=False)
    op.create_table('states',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['countries.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('cities',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('state_id', sa.Integer(), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['countries.id'], ),
    sa.ForeignKeyConstraint(['state_id'], ['states.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('clubs_and_committees',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=False),
    sa.Column('committee_name', sa.String(), nullable=True),
    sa.Column('position', sa.String(), nullable=True),
    sa.Column('education_id', sa.Integer(), nullable=True),
    sa.Column('starting_month', sa.Integer(), nullable=True),
    sa.Column('starting_year', sa.Integer(), nullable=True),
    sa.Column('ending_month', sa.Integer(), nullable=True),
    sa.Column('ending_year', sa.Integer(), nullable=True),
    sa.Column('is_currently_working', sa.Boolean(), nullable=True),
    sa.Column('responsibility_description', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['education_id'], ['higher_educations.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_clubs_and_committees_id'), 'clubs_and_committees', ['id'], unique=False)
    op.create_table('dsa_questions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('difficulty', sa.String(), nullable=True),
    sa.Column('time_minutes', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('ai_interviewed_job_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['ai_interviewed_job_id'], ['ai_interviewed_jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('interview_questions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('question', sa.String(), nullable=True),
    sa.Column('question_type', sa.String(), nullable=True),
    sa.Column('order_number', sa.Integer(), nullable=True),
    sa.Column('ai_interviewed_job_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['ai_interviewed_job_id'], ['ai_interviewed_jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('order_number', 'ai_interviewed_job_id', name='uq_order_job')
    )
    op.create_index(op.f('ix_interview_questions_id'), 'interview_questions', ['id'], unique=False)
    op.create_table('interviews',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('firstname', sa.String(), nullable=False),
    sa.Column('lastname', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('email_otp', sa.String(), nullable=True),
    sa.Column('email_otp_expiry', sa.DateTime(), nullable=True),
    sa.Column('email_verified', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('phone_otp', sa.String(), nullable=True),
    sa.Column('phone_otp_expiry', sa.DateTime(), nullable=True),
    sa.Column('phone_verified', sa.Boolean(), nullable=True),
    sa.Column('work_experience_yrs', sa.Integer(), nullable=True),
    sa.Column('education', sa.String(), nullable=True),
    sa.Column('skills', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('linkedin_url', sa.String(), nullable=True),
    sa.Column('portfolio_url', sa.String(), nullable=True),
    sa.Column('resume_url', sa.String(), nullable=True),
    sa.Column('resume_text', sa.String(), nullable=True),
    sa.Column('resume_match_score', sa.Integer(), nullable=True),
    sa.Column('resume_match_feedback', sa.String(), nullable=True),
    sa.Column('overall_score', sa.Integer(), nullable=True),
    sa.Column('technical_skills_score', sa.Integer(), nullable=True),
    sa.Column('communication_skills_score', sa.Integer(), nullable=True),
    sa.Column('problem_solving_skills_score', sa.Integer(), nullable=True),
    sa.Column('cultural_fit_score', sa.Integer(), nullable=True),
    sa.Column('feedback', sa.String(), nullable=True),
    sa.Column('report_file_url', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('ai_interviewed_job_id', sa.Integer(), nullable=False),
    sa.Column('private_link_token', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['ai_interviewed_job_id'], ['ai_interviewed_jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email', 'ai_interviewed_job_id', name='uq_email_job'),
    sa.UniqueConstraint('private_link_token')
    )
    op.create_index(op.f('ix_interviews_id'), 'interviews', ['id'], unique=False)
    op.create_table('job_applications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=False),
    sa.Column('job_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('resume_url', sa.String(), nullable=True),
    sa.Column('applied_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('job_seeker_id', 'job_id', name='uq_job_seeker_job')
    )
    op.create_index(op.f('ix_job_applications_id'), 'job_applications', ['id'], unique=False)
    op.create_table('quiz_questions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=True),
    sa.Column('time_seconds', sa.Integer(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('ai_interviewed_job_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['ai_interviewed_job_id'], ['ai_interviewed_jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dsa_responses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('passed', sa.Boolean(), nullable=True),
    sa.Column('interview_id', sa.Integer(), nullable=True),
    sa.Column('dsa_question_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['dsa_question_id'], ['dsa_questions.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['interview_id'], ['interviews.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('interview_id', 'dsa_question_id', name='uq_interview_and_question')
    )
    op.create_table('dsa_test_cases',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('input', sa.String(), nullable=True),
    sa.Column('expected_output', sa.String(), nullable=True),
    sa.Column('dsa_question_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['dsa_question_id'], ['dsa_questions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('interview_question_and_responses',
    sa.Column('question', sa.String(), nullable=False),
    sa.Column('question_type', sa.String(), nullable=True),
    sa.Column('order_number', sa.Integer(), nullable=False),
    sa.Column('answer', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('interview_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['interview_id'], ['interviews.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('order_number', 'interview_id')
    )
    op.create_table('interview_question_responses',
    sa.Column('answer', sa.String(), nullable=True),
    sa.Column('interview_question_id', sa.Integer(), nullable=False),
    sa.Column('interview_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['interview_id'], ['interviews.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['interview_question_id'], ['interview_questions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('interview_question_id', 'interview_id')
    )
    op.create_table('quiz_options',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('label', sa.String(), nullable=True),
    sa.Column('correct', sa.Boolean(), nullable=True),
    sa.Column('quiz_question_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['quiz_question_id'], ['quiz_questions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('dsa_test_case_responses',
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('dsa_response_id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.String(), nullable=True),
    sa.Column('dsa_test_case_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['dsa_response_id'], ['dsa_responses.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['dsa_test_case_id'], ['dsa_test_cases.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('dsa_response_id', 'dsa_test_case_id')
    )
    op.create_table('quiz_responses',
    sa.Column('interview_id', sa.Integer(), nullable=False),
    sa.Column('quiz_question_id', sa.Integer(), nullable=False),
    sa.Column('quiz_option_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['interview_id'], ['interviews.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['quiz_option_id'], ['quiz_options.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['quiz_question_id'], ['quiz_questions.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('interview_id', 'quiz_question_id', 'quiz_option_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('quiz_responses')
    op.drop_table('dsa_test_case_responses')
    op.drop_table('quiz_options')
    op.drop_table('interview_question_responses')
    op.drop_table('interview_question_and_responses')
    op.drop_table('dsa_test_cases')
    op.drop_table('dsa_responses')
    op.drop_table('quiz_questions')
    op.drop_index(op.f('ix_job_applications_id'), table_name='job_applications')
    op.drop_table('job_applications')
    op.drop_index(op.f('ix_interviews_id'), table_name='interviews')
    op.drop_table('interviews')
    op.drop_index(op.f('ix_interview_questions_id'), table_name='interview_questions')
    op.drop_table('interview_questions')
    op.drop_table('dsa_questions')
    op.drop_index(op.f('ix_clubs_and_committees_id'), table_name='clubs_and_committees')
    op.drop_table('clubs_and_committees')
    op.drop_table('cities')
    op.drop_table('states')
    op.drop_index(op.f('ix_ssc_educations_id'), table_name='ssc_educations')
    op.drop_table('ssc_educations')
    op.drop_index(op.f('ix_projects_id'), table_name='projects')
    op.drop_table('projects')
    op.drop_index(op.f('ix_jobs_id'), table_name='jobs')
    op.drop_table('jobs')
    op.drop_index(op.f('ix_internship_experiences_id'), table_name='internship_experiences')
    op.drop_table('internship_experiences')
    op.drop_index(op.f('ix_hsc_educations_id'), table_name='hsc_educations')
    op.drop_table('hsc_educations')
    op.drop_index(op.f('ix_higher_educations_id'), table_name='higher_educations')
    op.drop_table('higher_educations')
    op.drop_index(op.f('ix_employment_details_id'), table_name='employment_details')
    op.drop_table('employment_details')
    op.drop_index(op.f('ix_competitive_exams_id'), table_name='competitive_exams')
    op.drop_table('competitive_exams')
    op.drop_index(op.f('ix_certifications_id'), table_name='certifications')
    op.drop_table('certifications')
    op.drop_index(op.f('ix_ai_interviewed_jobs_id'), table_name='ai_interviewed_jobs')
    op.drop_table('ai_interviewed_jobs')
    op.drop_index(op.f('ix_academic_achievements_id'), table_name='academic_achievements')
    op.drop_table('academic_achievements')
    op.drop_index(op.f('ix_job_seekers_id'), table_name='job_seekers')
    op.drop_table('job_seekers')
    op.drop_table('countries')
    op.drop_index(op.f('ix_companies_id'), table_name='companies')
    op.drop_table('companies')
    # ### end Alembic commands ###
