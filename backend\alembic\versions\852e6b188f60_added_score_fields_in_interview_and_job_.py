"""added score fields in interview and job seeker

Revision ID: 852e6b188f60
Revises: 87051683aeea
Create Date: 2025-07-16 12:17:01.833527

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '852e6b188f60'
down_revision: Union[str, None] = '87051683aeea'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('dsa_responses', sa.Column('test_cases_passed', sa.Integer(), nullable=True))
    op.add_column('dsa_responses', sa.Column('test_cases_total', sa.Integer(), nullable=True))
    op.add_column('dsa_responses', sa.Column('time_taken_seconds', sa.Integer(), nullable=True))
    op.add_column('dsa_responses', sa.Column('memory_used_kb', sa.Integer(), nullable=True))
    op.add_column('interviews', sa.Column('quiz_score', sa.Integer(), nullable=True))
    op.add_column('interviews', sa.Column('dsa_score', sa.Integer(), nullable=True))
    op.add_column('job_seekers', sa.Column('edudiagno_score', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('job_seekers', 'edudiagno_score')
    op.drop_column('interviews', 'dsa_score')
    op.drop_column('interviews', 'quiz_score')
    op.drop_column('dsa_responses', 'memory_used_kb')
    op.drop_column('dsa_responses', 'time_taken_seconds')
    op.drop_column('dsa_responses', 'test_cases_total')
    op.drop_column('dsa_responses', 'test_cases_passed')
    # ### end Alembic commands ###
