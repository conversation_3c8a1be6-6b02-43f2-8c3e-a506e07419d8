"""added document

Revision ID: f1d39c1b8e63
Revises: cce001decbf7
Create Date: 2025-07-07 17:32:12.377592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1d39c1b8e63'
down_revision: Union[str, None] = 'cce001decbf7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('companies', sa.Column('document_type', sa.String(), nullable=True))
    op.add_column('companies', sa.Column('document_file_url', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('companies', 'document_file_url')
    op.drop_column('companies', 'document_type')
    # ### end Alembic commands ###
