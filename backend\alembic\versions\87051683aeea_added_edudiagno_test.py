"""added edudiagno test

Revision ID: 87051683aeea
Revises: 35b746fc687f
Create Date: 2025-07-16 12:06:01.560192

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '87051683aeea'
down_revision: Union[str, None] = '35b746fc687f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('edudiagno_test_responses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('edudiagno_test_id', sa.Integer(), nullable=False),
    sa.Column('job_seeker_id', sa.Integer(), nullable=True),
    sa.Column('resume_url', sa.String(), nullable=True),
    sa.Column('resume_text', sa.String(), nullable=True),
    sa.Column('resume_score', sa.Integer(), nullable=True),
    sa.Column('resume_feedback', sa.String(), nullable=True),
    sa.Column('edudiagno_score', sa.Integer(), nullable=True),
    sa.Column('technical_skills_score', sa.Integer(), nullable=True),
    sa.Column('communication_skills_score', sa.Integer(), nullable=True),
    sa.Column('problem_solving_skills_score', sa.Integer(), nullable=True),
    sa.Column('feedback', sa.String(), nullable=True),
    sa.Column('report_file_url', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_flagged', sa.Boolean(), nullable=True),
    sa.Column('admin_notes', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['edudiagno_test_id'], ['edudiagno_tests.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['job_seeker_id'], ['job_seekers.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('job_seeker_id', 'edudiagno_test_id', name='uq_jobseeker_test')
    )
    op.create_index(op.f('ix_edudiagno_test_responses_id'), 'edudiagno_test_responses', ['id'], unique=False)
    op.drop_index('ix_edudiagno_dsa_test_cases_id', table_name='edudiagno_dsa_test_cases')
    op.drop_table('edudiagno_dsa_test_cases')
    op.drop_index('ix_edudiagno_dsa_questions_id', table_name='edudiagno_dsa_questions')
    op.drop_table('edudiagno_dsa_questions')
    op.drop_index('ix_edudiagno_attempts_id', table_name='edudiagno_attempts')
    op.drop_table('edudiagno_attempts')
    op.drop_index('ix_edudiagno_video_questions_id', table_name='edudiagno_video_questions')
    op.drop_table('edudiagno_video_questions')
    op.drop_index('ix_edudiagno_mcq_questions_id', table_name='edudiagno_mcq_questions')
    op.drop_table('edudiagno_mcq_questions')
    op.add_column('dsa_questions', sa.Column('edudiagno_test_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'dsa_questions', 'edudiagno_tests', ['edudiagno_test_id'], ['id'], ondelete='CASCADE')
    op.add_column('dsa_responses', sa.Column('edudiagno_test_response_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'dsa_responses', 'edudiagno_test_responses', ['edudiagno_test_response_id'], ['id'], ondelete='CASCADE')
    op.add_column('edudiagno_tests', sa.Column('title', sa.String(), nullable=True))
    op.add_column('edudiagno_tests', sa.Column('department', sa.String(), nullable=True))
    op.add_column('edudiagno_tests', sa.Column('is_closed', sa.Boolean(), nullable=True))
    op.add_column('edudiagno_tests', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('edudiagno_tests', sa.Column('admin_notes', sa.String(), nullable=True))
    op.drop_constraint('edudiagno_tests_created_by_admin_id_fkey', 'edudiagno_tests', type_='foreignkey')
    op.drop_column('edudiagno_tests', 'is_active')
    op.drop_column('edudiagno_tests', 'created_by_admin_id')
    op.drop_column('edudiagno_tests', 'role')
    op.add_column('interview_question_responses', sa.Column('id', sa.Integer(), autoincrement=True, nullable=False))
    op.drop_constraint('interview_question_responses_pkey', 'interview_question_responses', type_='primary')
    op.create_primary_key('pk_interview_question_responses', 'interview_question_responses', ['id'])
    op.add_column('interview_question_responses', sa.Column('edudiagno_test_response_id', sa.Integer(), nullable=True))
    op.alter_column('interview_question_responses', 'interview_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.create_unique_constraint('uq_interview_question_response', 'interview_question_responses', ['interview_question_id', 'interview_id', 'edudiagno_test_response_id'])
    op.create_foreign_key(None, 'interview_question_responses', 'edudiagno_test_responses', ['edudiagno_test_response_id'], ['id'], ondelete='CASCADE')
    op.add_column('interview_questions', sa.Column('edudiagno_test_id', sa.Integer(), nullable=True))
    op.alter_column('interview_questions', 'ai_interviewed_job_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.create_foreign_key(None, 'interview_questions', 'edudiagno_tests', ['edudiagno_test_id'], ['id'], ondelete='CASCADE')
    op.add_column('quiz_questions', sa.Column('edudiagno_test_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'quiz_questions', 'edudiagno_tests', ['edudiagno_test_id'], ['id'], ondelete='CASCADE')
    # Step 1: Add id column as nullable and without autoincrement
    op.add_column('quiz_responses', sa.Column('id', sa.Integer(), nullable=True))
    # Step 1.5: Create sequence if not exists
    op.execute('CREATE SEQUENCE IF NOT EXISTS quiz_responses_id_seq OWNED BY quiz_responses.id;')
    # Step 2: Populate id for existing rows
    op.execute('UPDATE quiz_responses SET id = nextval(\'quiz_responses_id_seq\') WHERE id IS NULL;')
    # Step 3: Alter column to set NOT NULL and set default from sequence
    op.alter_column('quiz_responses', 'id', existing_type=sa.Integer(), nullable=False, server_default=sa.text("nextval('quiz_responses_id_seq')"))
    # Step 4: Drop old primary key and create new one
    op.drop_constraint('quiz_responses_pkey', 'quiz_responses', type_='primary')
    op.create_primary_key('pk_quiz_responses', 'quiz_responses', ['id'])
    op.add_column('quiz_responses', sa.Column('edudiagno_test_response_id', sa.Integer(), nullable=True))
    op.alter_column('quiz_responses', 'interview_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.create_unique_constraint('uq_quiz_response', 'quiz_responses', ['quiz_question_id', 'interview_id', 'edudiagno_test_response_id'])
    op.create_foreign_key(None, 'quiz_responses', 'edudiagno_test_responses', ['edudiagno_test_response_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'quiz_responses', type_='foreignkey')
    op.drop_constraint('uq_quiz_response', 'quiz_responses', type_='unique')
    op.alter_column('quiz_responses', 'interview_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('quiz_responses', 'edudiagno_test_response_id')
    op.drop_column('quiz_responses', 'id')
    op.drop_constraint(None, 'quiz_questions', type_='foreignkey')
    op.drop_column('quiz_questions', 'edudiagno_test_id')
    op.drop_constraint(None, 'interview_questions', type_='foreignkey')
    op.alter_column('interview_questions', 'ai_interviewed_job_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('interview_questions', 'edudiagno_test_id')
    op.drop_constraint(None, 'interview_question_responses', type_='foreignkey')
    op.drop_constraint('uq_interview_question_response', 'interview_question_responses', type_='unique')
    op.alter_column('interview_question_responses', 'interview_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('interview_question_responses', 'edudiagno_test_response_id')
    op.drop_column('interview_question_responses', 'id')
    op.add_column('edudiagno_tests', sa.Column('role', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('edudiagno_tests', sa.Column('created_by_admin_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('edudiagno_tests', sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.create_foreign_key('edudiagno_tests_created_by_admin_id_fkey', 'edudiagno_tests', 'admin_users', ['created_by_admin_id'], ['id'])
    op.drop_column('edudiagno_tests', 'admin_notes')
    op.drop_column('edudiagno_tests', 'is_deleted')
    op.drop_column('edudiagno_tests', 'is_closed')
    op.drop_column('edudiagno_tests', 'department')
    op.drop_column('edudiagno_tests', 'title')
    op.drop_constraint(None, 'dsa_responses', type_='foreignkey')
    op.drop_column('dsa_responses', 'edudiagno_test_response_id')
    op.drop_constraint(None, 'dsa_questions', type_='foreignkey')
    op.drop_column('dsa_questions', 'edudiagno_test_id')
    op.create_table('edudiagno_mcq_questions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('question', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('option_a', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('option_b', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('option_c', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('option_d', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('correct_answer', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('explanation', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('test_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['test_id'], ['edudiagno_tests.id'], name='edudiagno_mcq_questions_test_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='edudiagno_mcq_questions_pkey')
    )
    op.create_index('ix_edudiagno_mcq_questions_id', 'edudiagno_mcq_questions', ['id'], unique=False)
    op.create_table('edudiagno_video_questions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('question', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('question_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('time_limit_seconds', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('test_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['test_id'], ['edudiagno_tests.id'], name='edudiagno_video_questions_test_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='edudiagno_video_questions_pkey')
    )
    op.create_index('ix_edudiagno_video_questions_id', 'edudiagno_video_questions', ['id'], unique=False)
    op.create_table('edudiagno_attempts',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('candidate_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('test_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('started_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('completed_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('score', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('details', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['candidate_id'], ['job_seekers.id'], name='edudiagno_attempts_candidate_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['test_id'], ['edudiagno_tests.id'], name='edudiagno_attempts_test_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='edudiagno_attempts_pkey'),
    sa.UniqueConstraint('candidate_id', 'test_id', name='uq_candidate_test')
    )
    op.create_index('ix_edudiagno_attempts_id', 'edudiagno_attempts', ['id'], unique=False)
    op.create_table('edudiagno_dsa_questions',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('edudiagno_dsa_questions_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('difficulty', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('time_minutes', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('test_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['test_id'], ['edudiagno_tests.id'], name='edudiagno_dsa_questions_test_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='edudiagno_dsa_questions_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_edudiagno_dsa_questions_id', 'edudiagno_dsa_questions', ['id'], unique=False)
    op.create_table('edudiagno_dsa_test_cases',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('input', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('expected_output', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('dsa_question_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['dsa_question_id'], ['edudiagno_dsa_questions.id'], name='edudiagno_dsa_test_cases_dsa_question_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='edudiagno_dsa_test_cases_pkey')
    )
    op.create_index('ix_edudiagno_dsa_test_cases_id', 'edudiagno_dsa_test_cases', ['id'], unique=False)
    op.drop_index(op.f('ix_edudiagno_test_responses_id'), table_name='edudiagno_test_responses')
    op.drop_table('edudiagno_test_responses')
    # ### end Alembic commands ###
