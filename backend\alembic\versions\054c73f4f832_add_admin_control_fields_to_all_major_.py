"""Add admin control fields to all major models

Revision ID: 054c73f4f832
Revises: 45ba724e0ea1
Create Date: 2025-06-26 13:29:41.337565

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '054c73f4f832'
down_revision: Union[str, None] = '45ba724e0ea1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ai_interviewed_jobs', sa.Column('is_featured', sa.<PERSON>(), nullable=True))
    op.add_column('ai_interviewed_jobs', sa.Column('is_approved', sa.Boolean(), nullable=True))
    op.add_column('ai_interviewed_jobs', sa.Column('is_closed', sa.<PERSON>(), nullable=True))
    op.add_column('ai_interviewed_jobs', sa.Column('is_deleted', sa.<PERSON>(), nullable=True))
    op.add_column('ai_interviewed_jobs', sa.Column('admin_notes', sa.String(), nullable=True))
    op.add_column('companies', sa.Column('is_suspended', sa.Boolean(), nullable=True))
    op.add_column('companies', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('companies', sa.Column('admin_notes', sa.String(), nullable=True))
    op.add_column('dsa_questions', sa.Column('is_approved', sa.Boolean(), nullable=True))
    op.add_column('dsa_questions', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('dsa_questions', sa.Column('admin_notes', sa.String(), nullable=True))
    op.add_column('interview_questions', sa.Column('is_approved', sa.Boolean(), nullable=True))
    op.add_column('interview_questions', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('interview_questions', sa.Column('admin_notes', sa.String(), nullable=True))
    op.add_column('interviews', sa.Column('is_flagged', sa.Boolean(), nullable=True))
    op.add_column('interviews', sa.Column('admin_notes', sa.String(), nullable=True))
    op.add_column('job_applications', sa.Column('is_flagged', sa.Boolean(), nullable=True))
    op.add_column('job_applications', sa.Column('admin_notes', sa.String(), nullable=True))
    op.add_column('job_seekers', sa.Column('is_suspended', sa.Boolean(), nullable=True))
    op.add_column('job_seekers', sa.Column('is_verified', sa.Boolean(), nullable=True))
    op.add_column('job_seekers', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('job_seekers', sa.Column('admin_notes', sa.String(), nullable=True))
    op.add_column('jobs', sa.Column('is_featured', sa.Boolean(), nullable=True))
    op.add_column('jobs', sa.Column('is_approved', sa.Boolean(), nullable=True))
    op.add_column('jobs', sa.Column('is_closed', sa.Boolean(), nullable=True))
    op.add_column('jobs', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('jobs', sa.Column('admin_notes', sa.String(), nullable=True))
    op.drop_column('jobs', 'about_company')
    op.add_column('quiz_questions', sa.Column('is_approved', sa.Boolean(), nullable=True))
    op.add_column('quiz_questions', sa.Column('is_deleted', sa.Boolean(), nullable=True))
    op.add_column('quiz_questions', sa.Column('admin_notes', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('quiz_questions', 'admin_notes')
    op.drop_column('quiz_questions', 'is_deleted')
    op.drop_column('quiz_questions', 'is_approved')
    op.add_column('jobs', sa.Column('about_company', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('jobs', 'admin_notes')
    op.drop_column('jobs', 'is_deleted')
    op.drop_column('jobs', 'is_closed')
    op.drop_column('jobs', 'is_approved')
    op.drop_column('jobs', 'is_featured')
    op.drop_column('job_seekers', 'admin_notes')
    op.drop_column('job_seekers', 'is_deleted')
    op.drop_column('job_seekers', 'is_verified')
    op.drop_column('job_seekers', 'is_suspended')
    op.drop_column('job_applications', 'admin_notes')
    op.drop_column('job_applications', 'is_flagged')
    op.drop_column('interviews', 'admin_notes')
    op.drop_column('interviews', 'is_flagged')
    op.drop_column('interview_questions', 'admin_notes')
    op.drop_column('interview_questions', 'is_deleted')
    op.drop_column('interview_questions', 'is_approved')
    op.drop_column('dsa_questions', 'admin_notes')
    op.drop_column('dsa_questions', 'is_deleted')
    op.drop_column('dsa_questions', 'is_approved')
    op.drop_column('companies', 'admin_notes')
    op.drop_column('companies', 'is_deleted')
    op.drop_column('companies', 'is_suspended')
    op.drop_column('ai_interviewed_jobs', 'admin_notes')
    op.drop_column('ai_interviewed_jobs', 'is_deleted')
    op.drop_column('ai_interviewed_jobs', 'is_closed')
    op.drop_column('ai_interviewed_jobs', 'is_approved')
    op.drop_column('ai_interviewed_jobs', 'is_featured')
    # ### end Alembic commands ###
